<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-8">
    <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 max-w-4xl w-full">
      <!-- 标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">
          <i class="fas fa-clock mr-3"></i>
          活动监控测试页面
        </h1>
        <p class="text-gray-300 text-lg">测试 inactivityMonitor.stop() 和 restartInactivityMonitor 功能</p>
      </div>

      <!-- 状态显示区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- 监控状态 -->
        <div class="bg-white/5 rounded-xl p-6">
          <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            监控状态
          </h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-gray-300">监控状态:</span>
              <span :class="isMonitorActive ? 'text-green-400' : 'text-red-400'" class="font-semibold">
                <i :class="isMonitorActive ? 'fas fa-play' : 'fas fa-stop'" class="mr-1"></i>
                {{ isMonitorActive ? '运行中' : '已停止' }}
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-gray-300">倒计时状态:</span>
              <span :class="countdown > 0 ? 'text-yellow-400' : 'text-gray-400'" class="font-semibold">
                {{ countdown > 0 ? `${countdown}秒` : '未启动' }}
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-gray-300">App超时配置:</span>
              <span class="text-blue-400 font-semibold">
                {{ getAppTimeoutConfig() }}
              </span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="bg-white/5 rounded-xl p-6">
          <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-cogs mr-2"></i>
            操作控制
          </h3>
          <div class="space-y-3">
            <button
              @click="startMonitoring"
              :disabled="isMonitorActive"
              class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-300"
            >
              <i class="fas fa-play mr-2"></i>
              启动监控
            </button>
            <button
              @click="stopMonitoring"
              :disabled="!isMonitorActive"
              class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-300"
            >
              <i class="fas fa-stop mr-2"></i>
              停止监控
            </button>
            <button
              @click="startCountdownTest"
              :disabled="countdown > 0"
              class="w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-300"
            >
              <i class="fas fa-hourglass-start mr-2"></i>
              开始倒计时测试
            </button>
            <button
              @click="testRouteChange"
              class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-300"
            >
              <i class="fas fa-route mr-2"></i>
              测试路由变化重启
            </button>
          </div>
        </div>
      </div>

      <!-- 测试说明 -->
      <div class="bg-white/5 rounded-xl p-6 mb-8">
        <h3 class="text-xl font-semibold text-white mb-4">
          <i class="fas fa-list-ul mr-2"></i>
          测试步骤说明
        </h3>
        <div class="text-gray-300 space-y-2">
          <p><span class="text-yellow-400 font-semibold">1.</span> 点击"启动监控"按钮启动活动监控（使用App配置的超时时间）</p>
          <p><span class="text-yellow-400 font-semibold">2.</span> 点击"开始倒计时测试"按钮，这会调用 inactivityMonitor.stop() 停止监控</p>
          <p><span class="text-yellow-400 font-semibold">3.</span> 等待10秒倒计时结束</p>
          <p><span class="text-yellow-400 font-semibold">4.</span> 倒计时结束后会自动调用 restartInactivityMonitor() 重新启动监控</p>
          <p><span class="text-yellow-400 font-semibold">5.</span> 点击"测试路由变化重启"按钮测试路由变化时的监控重启功能</p>
          <p><span class="text-yellow-400 font-semibold">6.</span> 观察日志输出验证功能是否正常</p>
        </div>
      </div>

      <!-- 日志显示区域 -->
      <div class="bg-white/5 rounded-xl p-6 mb-8">
        <h3 class="text-xl font-semibold text-white mb-4">
          <i class="fas fa-terminal mr-2"></i>
          操作日志
        </h3>
        <div class="bg-black/30 rounded-lg p-4 h-64 overflow-y-auto">
          <div v-if="logs.length === 0" class="text-gray-400 text-center py-8">
            暂无日志记录
          </div>
          <div v-else class="space-y-1">
            <div
              v-for="(log, index) in logs"
              :key="index"
              class="text-sm font-mono text-green-400"
            >
              {{ log }}
            </div>
          </div>
        </div>
        <button
          @click="clearLogs"
          class="mt-3 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-all duration-300"
        >
          <i class="fas fa-trash mr-2"></i>
          清空日志
        </button>
      </div>

      <!-- 返回按钮 -->
      <div class="text-center">
        <button
          @click="goBack"
          class="px-8 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          返回首页
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import inactivityMonitor from "@/utils/inactivityMonitor";

export default {
  name: "InactivityMonitorTest",
  data() {
    return {
      countdown: 0,
      countdownDuration: 10, // 10秒倒计时
      timer: null,
      isMonitorActive: false,
      logs: []
    };
  },
  
  mounted() {
    this.addLog("📱 测试页面加载完成");
    this.updateMonitorStatus();
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    // 停止监控
    inactivityMonitor.stop();
    this.addLog("🔚 页面即将销毁，已停止监控");
  },
  
  methods: {
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.push(`[${timestamp}] ${message}`);
      // 自动滚动到底部
      this.$nextTick(() => {
        const logContainer = document.querySelector('.overflow-y-auto');
        if (logContainer) {
          logContainer.scrollTop = logContainer.scrollHeight;
        }
      });
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog("🧹 日志已清空");
    },
    
    // 开始倒计时测试
    startCountdownTest() {
      this.addLog("⏰ 开始倒计时测试 - 调用 inactivityMonitor.stop()");
      
      // 停止活动监控
      inactivityMonitor.stop();
      this.updateMonitorStatus();
      
      this.countdown = this.countdownDuration;
      this.addLog(`⏳ 倒计时开始：${this.countdown}秒`);
      
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
          if (this.countdown > 0) {
            this.addLog(`⏳ 倒计时：${this.countdown}秒`);
          }
        } else {
          clearInterval(this.timer);
          this.timer = null;
          this.addLog("⏰ 倒计时结束 - 调用 restartInactivityMonitor()");
          this.restartInactivityMonitor();
        }
      }, 1000);
    },
    
    // 重新启动活动监控
    restartInactivityMonitor() {
      this.addLog("🔄 重新启动活动监控");

      // 从父组件获取活动监控的配置
      const app = this.$parent;
      if (app && app.times && app.startInactivityMonitor) {
        this.addLog(`📋 使用App配置：超时时间 ${app.times/1000}秒`);
        app.startInactivityMonitor();
      } else {
        this.addLog("⚠️ 无法获取App配置，使用默认配置");
        // 如果无法从父组件获取配置，使用默认配置
        const defaultTimeout = 180000; // 默认3分钟
        const warningTime = 30000; // 警告提前30秒

        inactivityMonitor.start(
          () => {
            this.addLog("⚠️ 检测到无活动，触发超时回调");
            alert("检测到长时间无活动！将跳转到首页");
            this.$router.push("/");
          },
          defaultTimeout,
          (remainingSeconds) => {
            this.addLog(`⚠️ 警告：${remainingSeconds}秒后将超时`);
          },
          warningTime
        );
        this.addLog(`📋 使用默认配置：超时时间 ${defaultTimeout/1000}秒`);
      }
      this.updateMonitorStatus();
    },
    
    // 手动启动监控
    startMonitoring() {
      this.addLog("▶️ 手动启动活动监控");
      this.restartInactivityMonitor();
    },
    
    // 手动停止监控
    stopMonitoring() {
      this.addLog("⏹️ 手动停止活动监控");
      inactivityMonitor.stop();
      this.updateMonitorStatus();
    },
    
    // 更新监控状态
    updateMonitorStatus() {
      this.isMonitorActive = inactivityMonitor.isEnabled;
      this.addLog(`📊 监控状态更新：${this.isMonitorActive ? '运行中' : '已停止'}`);
    },
    
    // 获取App的超时配置
    getAppTimeoutConfig() {
      const app = this.$parent;
      if (app && app.times) {
        return `${app.times/1000}秒`;
      }
      return '未获取到配置';
    },

    // 返回首页
    goBack() {
      this.addLog("🏠 返回首页");
      this.$router.push("/");
    }
  }
};
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 按钮禁用状态样式 */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
